// <PERSON><PERSON>t to update Firebase Database Rules
// Run this script to apply the new database rules to Firebase

const admin = require('firebase-admin');
const fs = require('fs');

// Initialize Firebase Admin SDK
const serviceAccount = require('./path-to-your-service-account-key.json'); // You need to add your service account key

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://al-salamat-default-rtdb.firebaseio.com"
});

// Read the rules from database-rules.json
const rules = JSON.parse(fs.readFileSync('./database-rules.json', 'utf8'));

// Apply the rules to Firebase
async function updateDatabaseRules() {
  try {
    console.log('🔄 Updating Firebase Database Rules...');
    
    // Note: You need to manually update the rules in Firebase Console
    // or use Firebase CLI: firebase deploy --only database
    
    console.log('📋 New rules to apply:');
    console.log(JSON.stringify(rules, null, 2));
    
    console.log('\n✅ To apply these rules, run:');
    console.log('firebase deploy --only database');
    console.log('\nOr update manually in Firebase Console > Database > Rules');
    
  } catch (error) {
    console.error('❌ Error updating rules:', error);
  }
}

updateDatabaseRules();
