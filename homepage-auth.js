// Homepage Authentication Manager for AL-SALAMAT
// Handles authentication for the main website to access Firebase data
// FIXED: Removed automatic demo account creation to prevent unwanted user registrations

class HomepageAuthManager {
    constructor() {
        this.auth = null;
        this.database = null;
        this.currentUser = null;
        this.isAuthenticated = false;
        this.retryCount = 0;
        this.maxRetries = 2; // Reduced retries to prevent excessive account creation attempts
        this.authAttempted = false; // Track if authentication was already attempted
        this.init();
    }

    async init() {
        try {
            console.log('🔐 Initializing Homepage Authentication...');

            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                console.log('⏳ Waiting for Firebase...');
                setTimeout(() => this.init(), 1000);
                return;
            }

            this.auth = firebase.auth();
            this.database = firebase.database();

            // Always mark as authenticated first to ensure content loads immediately
            this.isAuthenticated = true;
            console.log('✅ Marked as authenticated for immediate content loading');

            // Initialize content managers immediately (don't wait for auth)
            this.onAuthSuccess();

            // Check authentication in background (non-blocking)
            this.checkExistingAuth().catch(error => {
                console.warn('⚠️ Auth check failed, but content already loaded:', error);
            });

            // Try anonymous authentication in background
            if (!this.authAttempted) {
                this.authAttempted = true;
                setTimeout(() => this.authenticateAnonymously(), 100);
            }

            console.log('✅ Homepage Authentication initialized');
        } catch (error) {
            console.error('❌ Error initializing homepage auth:', error);
            // Even if auth fails, ensure content loads
            this.isAuthenticated = true;
            this.onAuthSuccess();
        }
    }

    async checkExistingAuth() {
        return new Promise((resolve) => {
            // Set a timeout to ensure content loads even if onAuthStateChanged is slow
            const timeoutId = setTimeout(() => {
                console.log('⏰ Auth check timeout - loading content immediately');
                this.isAuthenticated = true;
                this.onAuthSuccess();
                resolve();
            }, 1000); // 1 second timeout

            this.auth.onAuthStateChanged((user) => {
                clearTimeout(timeoutId); // Clear timeout since auth state changed

                if (user) {
                    this.currentUser = user;
                    this.isAuthenticated = true;
                    console.log('👤 User already authenticated:', user.uid);
                    this.onAuthSuccess();
                } else {
                    console.log('🔓 No existing authentication - loading content immediately');
                    // Mark as authenticated to allow content loading
                    this.isAuthenticated = true;
                    this.onAuthSuccess();
                }
                resolve();
            });
        });
    }

    async authenticateAnonymously() {
        try {
            console.log('🔑 Attempting anonymous authentication...');

            const userCredential = await this.auth.signInAnonymously();
            this.currentUser = userCredential.user;
            this.isAuthenticated = true;

            console.log('✅ Anonymous authentication successful:', this.currentUser.uid);
            this.onAuthSuccess();

        } catch (error) {
            console.error('❌ Anonymous authentication failed:', error);

            // Mark as authenticated anyway to load real content
            this.isAuthenticated = true;
            this.onAuthSuccess();
        }
    }

    onAuthSuccess() {
        console.log('🎉 Authentication successful, initializing content managers...');
        
        // Trigger content loading
        this.initializeContentManagers();
        
        // Show success indicator
        this.showAuthStatus(true);
    }

    handleAuthFailure() {
        console.error('💥 Authentication failed - loading real content anyway');
        this.showAuthStatus(false);

        // Mark as authenticated to allow content managers to work
        this.isAuthenticated = true;

        // Initialize content managers to load real data from Firebase
        this.initializeContentManagers();
    }

    retryAuthentication() {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`🔄 Retrying authentication (${this.retryCount}/${this.maxRetries})...`);
            setTimeout(() => this.authenticateAnonymously(), 2000 * this.retryCount);
        } else {
            this.handleAuthFailure();
        }
    }

    initializeContentManagers() {
        console.log('🚀 Initializing content managers...');

        // Always load content directly first (immediate loading)
        this.loadContentDirectly();

        // Initialize dynamic content manager if available (additional loading)
        if (window.dynamicContentManager) {
            console.log('🔄 Reinitializing Dynamic Content Manager...');
            setTimeout(() => {
                window.dynamicContentManager.loadAllContent();
            }, 100);
        } else {
            console.log('⚠️ Dynamic Content Manager not available yet');
        }

        // Initialize enhanced realtime manager if available
        if (window.enhancedRealtimeManager) {
            console.log('⚡ Reinitializing Enhanced Realtime Manager...');
            setTimeout(() => {
                window.enhancedRealtimeManager.forceSync();
            }, 200);
        } else {
            console.log('⚠️ Enhanced Realtime Manager not available yet');
        }

        // Retry loading managers after a delay if they weren't available
        setTimeout(() => {
            if (!window.dynamicContentManager || !window.enhancedRealtimeManager) {
                console.log('🔄 Retrying content managers initialization...');
                this.initializeContentManagers();
            }
        }, 1000);
    }

    async loadContentDirectly() {
        try {
            console.log('📊 Loading content directly...');
            
            // Load company info
            await this.loadCompanyInfo();
            
            // Load branches
            await this.loadBranches();
            
            // Load contact info
            await this.loadContactInfo();
            
            // Load gallery
            await this.loadGallery();
            
            // Load site settings
            await this.loadSiteSettings();
            
            console.log('✅ Content loaded directly');
        } catch (error) {
            console.error('❌ Error loading content directly:', error);
        }
    }

    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            
            if (data) {
                // Update company title
                const titleElement = document.getElementById('company-title');
                if (titleElement && data.title) {
                    titleElement.textContent = data.title;
                }
                
                // Update company subtitle
                const subtitleElement = document.getElementById('company-subtitle');
                if (subtitleElement && data.subtitle) {
                    subtitleElement.textContent = data.subtitle;
                }
                
                // Update company description
                const descriptionElement = document.getElementById('company-description');
                if (descriptionElement && data.description) {
                    descriptionElement.textContent = data.description;
                }
                
                console.log('✅ Company info loaded');
            }
        } catch (error) {
            console.error('❌ Error loading company info:', error);
        }
    }

    async loadBranches() {
        try {
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();
            
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');
            
            if (!branchesGrid) return;
            
            // Clear existing content
            branchesGrid.innerHTML = '';
            
            if (data && Object.keys(data).length > 0) {
                // Hide no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                }
                
                // Add branches
                Object.entries(data).forEach(([branchId, branch]) => {
                    const branchCard = document.createElement('div');
                    branchCard.className = 'branch-card';
                    branchCard.innerHTML = `
                        <h3>${this.escapeHtml(branch.name || 'فرع غير محدد')}</h3>
                        <p><strong>العنوان:</strong> ${this.escapeHtml(branch.address || 'عنوان غير محدد')}</p>
                        ${branch.phone ? `<p><strong>الهاتف:</strong> ${this.escapeHtml(branch.phone)}</p>` : ''}
                        <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}" 
                           target="_blank" 
                           class="location-btn">
                           📍 الموقع
                        </a>
                    `;
                    branchesGrid.appendChild(branchCard);
                });
                
                console.log(`✅ Loaded ${Object.keys(data).length} branches`);
            } else {
                // Show no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'block';
                } else {
                    branchesGrid.innerHTML = '<div class="no-data-message"><p>لا توجد فروع مضافة حالياً.</p></div>';
                }
                console.log('ℹ️ No branches data available');
            }
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    async loadContactInfo() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();
            
            if (data) {
                // Update contact elements
                const updates = [
                    { id: 'contact-title', value: data.title },
                    { id: 'contact-info-title', value: data.infoTitle },
                    { id: 'contact-address-display', value: data.address },
                    { id: 'contact-hours-display', value: data.hours }
                ];

                updates.forEach(update => {
                    const element = document.getElementById(update.id);
                    if (element && update.value) {
                        element.textContent = update.value;
                    }
                });
                
                console.log('✅ Contact info loaded');
            }
        } catch (error) {
            console.error('❌ Error loading contact info:', error);
        }
    }

    async loadGallery() {
        try {
            const snapshot = await this.database.ref('gallery').once('value');
            const data = snapshot.val();
            
            const galleryGrid = document.getElementById('dynamic-gallery');
            if (!galleryGrid) return;
            
            // Remove only Firebase images
            const firebaseImages = galleryGrid.querySelectorAll('.firebase-image');
            firebaseImages.forEach(img => img.remove());
            
            if (data && Object.keys(data).length > 0) {
                Object.values(data).forEach(image => {
                    const galleryItem = document.createElement('div');
                    galleryItem.className = 'gallery-item firebase-image';
                    galleryItem.innerHTML = `
                        <img src="${this.escapeHtml(image.url)}" 
                             alt="${this.escapeHtml(image.alt || 'صورة من المعرض')}" 
                             class="gallery-image" loading="lazy">
                    `;
                    galleryGrid.appendChild(galleryItem);
                });
                
                console.log(`✅ Loaded ${Object.keys(data).length} gallery images`);
            }
        } catch (error) {
            console.error('❌ Error loading gallery:', error);
        }
    }

    async loadSiteSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();

            if (data) {
                // Update email
                if (data.contactEmail) {
                    const emailElement = document.getElementById('contact-email-display');
                    const emailLink = document.getElementById('contact-email-link');
                    if (emailElement) {
                        emailElement.textContent = data.contactEmail;
                    }
                    if (emailLink) {
                        emailLink.href = `mailto:${data.contactEmail}`;
                    }
                }

                // Update phone
                if (data.contactPhone) {
                    const phoneElement = document.getElementById('contact-phone-display');
                    const phoneLink = document.getElementById('contact-phone-link');
                    if (phoneElement) {
                        phoneElement.textContent = data.contactPhone;
                    }
                    if (phoneLink) {
                        phoneLink.href = `tel:${data.contactPhone}`;
                    }
                }

                console.log('✅ Site settings loaded');
            }
        } catch (error) {
            console.error('❌ Error loading site settings:', error);
        }
    }

    showAuthStatus(success) {
        // Update connection status if available
        const statusElement = document.getElementById('sync-status');
        if (statusElement) {
            if (success) {
                statusElement.className = 'sync-status online';
                statusElement.textContent = '🟢 متصل';
            } else {
                statusElement.className = 'sync-status offline';
                statusElement.textContent = '🔴 غير متصل';
            }
        }
    }





    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public methods
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    async forceReload() {
        if (this.isAuthenticated) {
            await this.loadContentDirectly();
        } else {
            await this.init();
        }
    }
}

// Global functions
window.reloadHomepageContent = function() {
    if (window.homepageAuth) {
        window.homepageAuth.forceReload();
    }
};

window.getHomepageAuthStatus = function() {
    if (window.homepageAuth) {
        return {
            isAuthenticated: window.homepageAuth.isUserAuthenticated(),
            user: window.homepageAuth.getCurrentUser()
        };
    }
    return { isAuthenticated: false, user: null };
};

// Initialize Homepage Authentication
function initializeHomepageAuth() {
    if (typeof firebase !== 'undefined') {
        window.homepageAuth = new HomepageAuthManager();
        console.log('✅ Homepage Authentication Manager started');
    } else {
        console.log('⏳ Firebase not ready, retrying...');
        setTimeout(initializeHomepageAuth, 500);
    }
}

// Emergency content loading function (fallback)
function emergencyContentLoad() {
    console.log('🚨 Emergency content loading activated');

    if (typeof firebase !== 'undefined' && firebase.database) {
        const database = firebase.database();

        // Load essential content immediately
        Promise.all([
            database.ref('branches').once('value'),
            database.ref('aboutSection').once('value'),
            database.ref('contactSection').once('value'),
            database.ref('siteSettings').once('value')
        ]).then(([branchesSnapshot, aboutSnapshot, contactSnapshot, settingsSnapshot]) => {
            console.log('🔥 Emergency content loaded successfully');

            // Update branches
            const branchesData = branchesSnapshot.val();
            if (branchesData) {
                const branchesGrid = document.getElementById('dynamic-branches');
                if (branchesGrid) {
                    branchesGrid.innerHTML = '';
                    Object.entries(branchesData).forEach(([id, branch]) => {
                        const branchCard = document.createElement('div');
                        branchCard.className = 'branch-card';
                        branchCard.innerHTML = `
                            <h3>${branch.name || 'فرع غير محدد'}</h3>
                            <p><strong>العنوان:</strong> ${branch.address || 'عنوان غير محدد'}</p>
                            ${branch.phone ? `<p><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}"
                               target="_blank" class="location-btn">📍 الموقع</a>
                        `;
                        branchesGrid.appendChild(branchCard);
                    });
                }
            }

            // Update about section
            const aboutData = aboutSnapshot.val();
            if (aboutData && aboutData.description) {
                const aboutDescription = document.getElementById('about-description');
                const aboutLoading = document.getElementById('about-loading');
                if (aboutDescription) {
                    aboutDescription.textContent = aboutData.description;
                    aboutDescription.classList.remove('hidden');
                }
                if (aboutLoading) aboutLoading.style.display = 'none';
            }

            // Update contact info
            const contactData = contactSnapshot.val();
            if (contactData) {
                if (contactData.address) {
                    const addressElement = document.getElementById('contact-address-display');
                    if (addressElement) addressElement.textContent = contactData.address;
                }
                if (contactData.hours) {
                    const hoursElement = document.getElementById('contact-hours-display');
                    if (hoursElement) hoursElement.textContent = contactData.hours;
                }
            }

            // Update site settings
            const settingsData = settingsSnapshot.val();
            if (settingsData) {
                if (settingsData.contactEmail) {
                    const emailElement = document.getElementById('contact-email-display');
                    const emailLink = document.getElementById('contact-email-link');
                    if (emailElement) emailElement.textContent = settingsData.contactEmail;
                    if (emailLink) emailLink.href = `mailto:${settingsData.contactEmail}`;
                }
                if (settingsData.contactPhone) {
                    const phoneElement = document.getElementById('contact-phone-display');
                    const phoneLink = document.getElementById('contact-phone-link');
                    if (phoneElement) phoneElement.textContent = settingsData.contactPhone;
                    if (phoneLink) phoneLink.href = `tel:${settingsData.contactPhone}`;
                }
            }

        }).catch(error => {
            console.error('❌ Emergency content loading failed:', error);
        });
    }
}

// Auto-initialize with emergency fallback
document.addEventListener('DOMContentLoaded', () => {
    // Start homepage auth immediately
    setTimeout(initializeHomepageAuth, 100);

    // Emergency content loading as fallback
    setTimeout(emergencyContentLoad, 500);

    // Additional emergency loading if still no content
    setTimeout(() => {
        const branchesGrid = document.getElementById('dynamic-branches');
        const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];

        if (branchCards.length === 0) {
            console.log('🚨 No content detected, triggering emergency load');
            emergencyContentLoad();
        }
    }, 2000);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.homepageAuth && window.homepageAuth.auth) {
        // Sign out anonymous users to clean up
        if (window.homepageAuth.currentUser && window.homepageAuth.currentUser.isAnonymous) {
            window.homepageAuth.auth.signOut();
        }
    }
});
