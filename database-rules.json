{"rules": {".read": true, ".write": false, "users": {".read": "auth != null", ".write": "auth != null", "$userId": {".read": "auth != null", ".write": "auth != null || auth.uid == $userId"}}, "contactForms": {".read": "auth != null", ".write": true}, "loginActivity": {".read": "auth != null", ".write": "auth != null"}, "branches": {".read": true, ".write": "auth != null"}, "gallery": {".read": true, ".write": "auth != null"}, "galleryImages": {".read": true, ".write": "auth != null"}, "siteContent": {".read": true, ".write": "auth != null"}, "aboutSection": {".read": true, ".write": "auth != null"}, "branchesSection": {".read": true, ".write": "auth != null"}, "contactSection": {".read": true, ".write": "auth != null"}, "siteSettings": {".read": true, ".write": "auth != null"}, "bannerSettings": {".read": true, ".write": "auth != null"}, "serviceImages": {".read": true, ".write": "auth != null"}, "heroImages": {".read": true, ".write": "auth != null"}, "serviceRequests": {".read": "auth != null", ".write": "auth != null"}, "test": {".read": true, ".write": true}}}